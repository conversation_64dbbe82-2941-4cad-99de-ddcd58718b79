<template>
  <div class="drawer-box">
    <customDrawer :show.sync="visible" :loading="isLoading" :title="'销样时间'" :size="800" @confirm="saveSetting">
      <div class="drawer-container">
        <div class="drawer-content">
          <el-input-number
            v-model="drawerFormData.time"
            controls-position="right"
            :min="0"
            @input.native="handleNativeInput($event, 'time')"
          ></el-input-number>
          <span style="color: #ff9b45" class="m-r-10">时</span>
          <el-input-number
            v-model="drawerFormData.divide"
            controls-position="right"
            :min="0"
            @input.native="handleNativeInput($event, 'divide')"
          ></el-input-number>
          <span style="color: #ff9b45">分</span>
          <span class="m-l-10">自动销样</span>
          <div style="color: #ff9b45" class="m-t-10">未设置则以取样时间作为销样时间</div>
        </div>
      </div>
    </customDrawer>
  </div>
</template>

<script>
export default {
  props: {
    isshow: Boolean,
    drawerData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isLoading: false,
      drawerFormData: {
        time: '',
        divide: ''
      }
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  mounted() {
    this.getRecordGetDestroyTime()
  },
  methods: {
    closeClick() {
      this.visible = false
    },
    async getRecordGetDestroyTime() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundStoreRetentionRecordGetDestroyTimePost()
      this.isLoading = false
      if (res && res.code === 0) {
        // 统计
      } else {
        this.$message.error(res.msg)
      }
    },
    handleNativeInput(e, type) {
      const value = e.target.value
      // 只保留数字
      const intValue = value.replace(/[^0-9]/g, '')
      this.drawerFormData[type] = intValue === '' ? null : Number(intValue)
    },
    // async delDrawerForm() {
    // },
    saveSetting() {}
  }
}
</script>

<style lang="scss" scoped>
.drawer-box {
  .drawer-container {
    position: relative;
    height: 100%;
    overflow: hidden;
    .drawer-content {
      padding: 0 20px;
    }
    .drawer-footer {
      position: absolute;
      bottom: 20px;
      left: 20px;
      width: 100%;
      text-align: left;
    }
  }
  ::v-deep .el-drawer__header {
    margin-bottom: 0;
    padding: 23px 20px;
    background: #e7e9ef;
  }
}
</style>
